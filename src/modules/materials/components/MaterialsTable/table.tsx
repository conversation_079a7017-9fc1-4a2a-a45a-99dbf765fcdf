import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "~/core/components/tables/BasicTable";
import type { Product } from "~/modules/product/service/model/product";
import { columns } from "./columns";

interface Props {
	products: Product[];
}

export default function Table({ products }: Props) {
	const table = useReactTable({
		data: products,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
