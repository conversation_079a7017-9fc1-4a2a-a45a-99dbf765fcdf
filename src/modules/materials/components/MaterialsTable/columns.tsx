import { Link } from "@tanstack/react-router";
import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import { useState } from "react";
import type { Product } from "~/product/service/model/product";
import DeleteMaterialModal from "../DeleteMaterialModal";

const columnHelper = createColumnHelper<Product>();

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("commercialName", {
		header: "Nombre Comercial",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("code", {
		header: "Código",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("skuCode", {
		header: "SKU",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("costPrice", {
		header: "Precio",
		cell: (info) => {
			const value = info.getValue();
			return value ? `$${value.toFixed(2)}` : "-";
		},
	}),
	columnHelper.accessor("costPriceTotal", {
		header: "Precio Total",
		cell: (info) => {
			const value = info.getValue();
			return value ? `$${value.toFixed(2)}` : "-";
		},
	}),
	columnHelper.accessor("canBeSold", {
		header: "Se Vende",
		cell: (info) => (
			<span
				className={`badge ${info.getValue() ? "badge-success" : "badge-error"}`}
			>
				{info.getValue() ? "Sí" : "No"}
			</span>
		),
	}),
	columnHelper.accessor("canBePurchased", {
		header: "Se Compra",
		cell: (info) => (
			<span
				className={`badge ${info.getValue() ? "badge-success" : "badge-error"}`}
			>
				{info.getValue() ? "Sí" : "No"}
			</span>
		),
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isOpen, setIsOpen] = useState(false);
			const product = row.original;

			return (
				<div className="flex gap-2">
					<Link
						to={"/admin/products/materials/edit/$id"}
						params={{ id: product.id }}
						className="btn btn-sm btn-primary"
					>
						<Edit size={16} />
					</Link>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsOpen(true)}
					>
						<Trash size={16} />
					</button>
					<DeleteMaterialModal
						isOpen={isOpen}
						setIsOpen={setIsOpen}
						product={product}
					/>
				</div>
			);
		},
	}),
];
