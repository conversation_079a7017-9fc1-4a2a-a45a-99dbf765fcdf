import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { CategoryCode } from "~/category/service/model/category";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { productOptionsByCategoryCode } from "~/product/hooks/product-options";
import Table from "./table";

export default function MaterialsTable() {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery(
		productOptionsByCategoryCode(svc, CategoryCode.MATERIALS),
	);

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) return <div>Error: {getErrorResult(error).error.message}</div>;

	if (isPending) return <div>Loading...</div>;

	return <Table products={data || []} />;
}
