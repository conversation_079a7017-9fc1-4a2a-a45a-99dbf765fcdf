import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { brandOptions } from "~/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "~/category/hooks/category-options";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import { measurementUnitOptions } from "~/measurement-unit/hooks/measurement-unit-options";
import { CategoryCode } from "~/modules/category/service/model/category";
import useCreateProduct from "~/product/hooks/use-create-product";
import { CreateProductionDeviceSchema } from "./schema";

const defaultValues = {
	canBeSold: false,
	canBePurchased: true,
	costPrice: 0,
} as CreateProductionDeviceSchema;

export default function useCreateProductionDeviceForm() {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useCreateProduct();

	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);
	const { data: categories = [] } = useQuery(categoryOptions(service));

	const productionDeviceCategory = categories.find(
		(cat) => cat.code === CategoryCode.PRODUCTION_DEVICES,
	);

	const { data: productionDeviceSubcategories = [] } = useQuery({
		...categorySubcategoriesOptions(
			service,
			productionDeviceCategory?.id || "",
		),
		enabled: !!productionDeviceCategory?.id,
	});

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateProductionDeviceSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: productionDeviceCategory?.id
						? [...value.categoryIDs, productionDeviceCategory?.id]
						: value.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
					costPriceTotal: value.costPriceTotal,
				},
				{
					onSuccess: () => {
						toast.success("Dispositivo de producción creado exitosamente");
						navigate({ to: "/admin/products/production-devices" });
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	return {
		form,
		isPending,
		brands,
		measurementUnits,
		productionDeviceSubcategories,
	};
}
