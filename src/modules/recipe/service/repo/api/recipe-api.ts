import { Http<PERSON><PERSON> } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "src/core/service/repo/api";
import {
	handleDResponse,
	handleResponse,
} from "src/core/service/repo/api/utils";
import type { CreateRecipe, UpdateRecipe } from "../../model/recipe";
import { RecipeRepository } from "../../model/repository";
import {
	RecipeFromApi,
	RecipeListFromApi,
	CreateRecipeApiFromCreateRecipe,
	CreateRecipeApiResponse,
	UpdateRecipeApiFromUpdateRecipe,
} from "./dto";

const baseUrl = "/v1/recipes";

const makeRecipeApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(RecipeListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(RecipeFromApi))),
		create: (recipe: CreateRecipe) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateRecipeApiFromCreateRecipe)(recipe),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateRecipeApiResponse))),
		update: (recipe: UpdateRecipe) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateRecipeApiFromUpdateRecipe)(recipe),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate-code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
		validateName: (name: string) =>
			httpClient
				.get(`${baseUrl}/validate-name/${name}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const recipeApiRepoLive = Layer.effect(RecipeRepository, makeRecipeApiRepo);
