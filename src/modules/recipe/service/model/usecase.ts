import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type { Recipe, CreateRecipe, UpdateRecipe } from "./recipe";

export class RecipeUsecase extends Effect.Tag("RecipeUsecase")<
	RecipeUsecase,
	{
		readonly getAll: () => Effect.Effect<Recipe[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Recipe, AppError>;
		readonly create: (recipe: CreateRecipe) => Effect.Effect<string, AppError>;
		readonly update: (recipe: UpdateRecipe) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
		readonly validateName: (name: string) => Effect.Effect<void, AppError>;
	}
>() {}
