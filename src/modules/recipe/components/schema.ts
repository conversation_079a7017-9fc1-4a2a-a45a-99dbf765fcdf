import * as v from "valibot";

export const CreateRecipeSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	type: v.pipe(
		v.string("Debe seleccionar un tipo"),
		v.minLength(1, "Debe seleccionar un tipo"),
	),
	productIDs: v.array(v.string()),
	components: v.array(
		v.object({
			productID: v.string("Debe seleccionar un producto"),
			quantity: v.pipe(
				v.number("Debe ingresar una cantidad"),
				v.minValue(0.01, "La cantidad debe ser mayor a 0"),
			),
		}),
	),
});
export type CreateRecipeSchema = v.InferOutput<typeof CreateRecipeSchema>;
