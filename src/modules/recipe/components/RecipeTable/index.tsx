import useRecipes from "../../hooks/use-recipes";
import Table from "./table";

export default function RecipeTable() {
	const { data: recipes = [], isPending, isError } = useRecipes();

	if (isPending) {
		return (
			<div className="flex justify-center p-8">
				<span className="loading loading-spinner loading-lg"></span>
			</div>
		);
	}

	if (isError) {
		return (
			<div className="alert alert-error">
				<span>Error al cargar las recetas</span>
			</div>
		);
	}

	return <Table recipes={recipes} />;
}
