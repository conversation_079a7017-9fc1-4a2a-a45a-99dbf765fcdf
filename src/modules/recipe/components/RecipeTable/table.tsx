import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "src/core/components/tables/BasicTable";
import type { Recipe } from "../../service/model/recipe";
import { columns } from "./columns";

interface Props {
	recipes: Recipe[];
}

export default function Table({ recipes }: Props) {
	const table = useReactTable({
		data: recipes,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
