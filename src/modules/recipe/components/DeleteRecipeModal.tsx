import { toast } from "react-toastify";
import CloseModal from "src/core/components/CloseModal";
import { getErrorResult } from "src/core/utils/effectErrors";
import { cn } from "src/core/utils/classes";
import useDeleteRecipe from "../hooks/use-delete-recipe";
import type { Recipe } from "../service/model/recipe";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	recipe: Recipe;
}

export default function DeleteRecipeModal({ isOpen, setIsOpen, recipe }: Props) {
	const { mutate, isPending } = useDeleteRecipe();

	function handleDelete() {
		mutate(recipe.id, {
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
			onSettled: () => {
				setIsOpen(false);
				toast.success("Receta eliminada exitosamente");
			},
		});
	}

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar Receta</h3>
				<div className="space-y-4">
					<p>
						¿Estás seguro de que deseas eliminar la receta{" "}
						<strong>{recipe.name}</strong>?
					</p>
					<p className="text-base-content/70 text-sm">
						Esta acción no se puede deshacer.
					</p>
					<div className="modal-action">
						<button
							type="button"
							className="btn btn-ghost"
							onClick={() => setIsOpen(false)}
							disabled={isPending}
						>
							Cancelar
						</button>
						<button
							type="button"
							className="btn btn-error"
							onClick={handleDelete}
							disabled={isPending}
						>
							{isPending ? (
								<span className="loading loading-spinner loading-sm" />
							) : (
								"Eliminar"
							)}
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
