import { Plus } from "lucide-react";
import { Link } from "@tanstack/react-router";
import RecipeTable from "../RecipeTable";

export default function RecipeManagement() {
	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl">Gestión de Recetas</h1>
				<Link
					to="/admin/manufacture/recipes/create"
					className="btn btn-primary"
				>
					<Plus size={16} />
					Nueva Receta
				</Link>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<RecipeTable />
				</div>
			</div>
		</div>
	);
}
