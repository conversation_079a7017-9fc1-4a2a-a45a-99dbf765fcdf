import { useMutation } from "@tanstack/react-query";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";

export default function useValidateRecipeName() {
	const service = useService();
	const { recipe } = service;

	return useMutation({
		mutationFn: (name: string) =>
			AppRuntime.runPromise(recipe.validateName(name)),
	});
}
