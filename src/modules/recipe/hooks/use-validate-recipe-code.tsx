import { useMutation } from "@tanstack/react-query";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";

export default function useValidateRecipeCode() {
	const service = useService();
	const { recipe } = service;

	return useMutation({
		mutationFn: (code: string) =>
			AppRuntime.runPromise(recipe.validateCode(code)),
	});
}
