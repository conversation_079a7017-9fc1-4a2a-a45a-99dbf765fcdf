import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const recipeOptions = ({ recipe }: serviceRegistry) =>
	queryOptions({
		queryKey: ["recipes"],
		queryFn: () => AppRuntime.runPromise(recipe.getAll()),
	});

export const recipeOptionsById = ({ recipe }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["recipes", id],
		queryFn: () => AppRuntime.runPromise(recipe.getById(id)),
	});
