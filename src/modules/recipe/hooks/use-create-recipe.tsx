import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Recipe, CreateRecipe } from "../service/model/recipe";
import { recipeOptions } from "./recipe-options";

export default function useCreateRecipe() {
	const service = useService();
	const { recipe } = service;
	const queryClient = useQueryClient();
	const queryKey = recipeOptions(service).queryKey;

	return useMutation({
		mutationFn: (newRecipe: CreateRecipe) =>
			AppRuntime.runPromise(recipe.create(newRecipe)),
		onMutate: async (newRecipe) => {
			await queryClient.cancelQueries({ queryKey });
			const previousRecipes = queryClient.getQueryData<Recipe[]>(queryKey);

			queryClient.setQueryData<Recipe[]>(queryKey, (old) =>
				create(old, (draft) => {
					if (draft) {
						draft.push({
							id: "temp-id",
							...newRecipe,
							products: [],
							components: [],
							createdAt: new Date().toISOString(),
							updatedAt: new Date().toISOString(),
							deletedAt: null,
						});
					}
				}),
			);

			return { previousRecipes };
		},
		onError: (err, newRecipe, context) => {
			if (context?.previousRecipes) {
				queryClient.setQueryData(queryKey, context.previousRecipes);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
