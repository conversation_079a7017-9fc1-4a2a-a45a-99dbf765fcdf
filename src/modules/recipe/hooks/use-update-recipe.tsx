import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Recipe, UpdateRecipe } from "../service/model/recipe";
import { recipeOptions } from "./recipe-options";

export default function useUpdateRecipe() {
	const service = useService();
	const { recipe } = service;
	const queryClient = useQueryClient();
	const queryKey = recipeOptions(service).queryKey;

	return useMutation({
		mutationFn: (updatedRecipe: UpdateRecipe) =>
			AppRuntime.runPromise(recipe.update(updatedRecipe)),
		onMutate: async (updatedRecipe) => {
			await queryClient.cancelQueries({ queryKey });
			const previousRecipes = queryClient.getQueryData<Recipe[]>(queryKey);

			queryClient.setQueryData<Recipe[]>(queryKey, (old) =>
				create(old, (draft) => {
					if (draft) {
						const index = draft.findIndex((r) => r.id === updatedRecipe.id);
						if (index !== -1) {
							draft[index] = {
								...draft[index],
								...updatedRecipe,
								updatedAt: new Date().toISOString(),
							};
						}
					}
				}),
			);

			return { previousRecipes };
		},
		onError: (err, updatedRecipe, context) => {
			if (context?.previousRecipes) {
				queryClient.setQueryData(queryKey, context.previousRecipes);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
