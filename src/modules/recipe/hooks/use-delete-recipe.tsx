import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Recipe } from "../service/model/recipe";
import { recipeOptions } from "./recipe-options";

export default function useDeleteRecipe() {
	const service = useService();
	const { recipe } = service;
	const queryClient = useQueryClient();
	const queryKey = recipeOptions(service).queryKey;

	return useMutation({
		mutationFn: (id: string) => AppRuntime.runPromise(recipe.delete(id)),
		onMutate: async (id) => {
			await queryClient.cancelQueries({ queryKey });
			const previousRecipes = queryClient.getQueryData<Recipe[]>(queryKey);

			queryClient.setQueryData<Recipe[]>(queryKey, (old) =>
				create(old, (draft) => {
					if (draft) {
						const index = draft.findIndex((r) => r.id === id);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}
				}),
			);

			return { previousRecipes };
		},
		onError: (err, id, context) => {
			if (context?.previousRecipes) {
				queryClient.setQueryData(queryKey, context.previousRecipes);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
