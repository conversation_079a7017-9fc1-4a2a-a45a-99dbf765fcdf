import { Http<PERSON><PERSON> } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "src/core/service/repo/api";
import {
	handleDResponse,
	handleResponse,
} from "src/core/service/repo/api/utils";
import type { CreateProduct, UpdateProduct } from "../../model/product";
import { ProductRepository } from "../../model/repository";
import {
	CreateProductApiFromCreateProduct,
	CreateProductApiResponse,
	ProductFromApi,
	ProductListFromApi,
	UpdateProductApiFromUpdateProduct,
} from "./dto";

const baseUrl = "/v1/products";

const makeProductApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(ProductListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(ProductFromApi))),
		create: (product: CreateProduct) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateProductApiFromCreateProduct)(
							product,
						),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateProductApiResponse))),
		update: (product: UpdateProduct) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateProductApiFromUpdateProduct)(
							product,
						),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate-code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
		validateCommercialName: (commercialName: string) =>
			httpClient
				.get(`${baseUrl}/validate-commercial-name/${commercialName}`)
				.pipe(Effect.flatMap(handleResponse)),
		validateSKUCode: (skuCode: string) =>
			httpClient
				.get(`${baseUrl}/validate-sku-code/${skuCode}`)
				.pipe(Effect.flatMap(handleResponse)),
		getProductsByCategoryCode: (categoryCode: string) =>
			httpClient
				.get(`${baseUrl}/category/${categoryCode}`)
				.pipe(Effect.flatMap(handleDResponse(ProductListFromApi))),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const productApiRepoLive = Layer.effect(
	ProductRepository,
	makeProductApiRepo,
);
