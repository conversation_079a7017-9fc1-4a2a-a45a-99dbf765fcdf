import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type { CreateProduct, Product, UpdateProduct } from "./product";

export class ProductRepository extends Effect.Tag("ProductRepository")<
	ProductRepository,
	{
		readonly getAll: () => Effect.Effect<Product[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Product, AppError>;
		readonly create: (
			product: CreateProduct,
		) => Effect.Effect<string, AppError>;
		readonly update: (product: UpdateProduct) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
		readonly validateCommercialName: (
			commercialName: string,
		) => Effect.Effect<void, AppError>;
		readonly validateSKUCode: (
			skuCode: string,
		) => Effect.Effect<void, AppError>;
		readonly getProductsByCategoryCode: (
			categoryCode: string,
		) => Effect.Effect<Product[], AppError>;
	}
>() {}
