import { Store } from "@tanstack/react-store";

export enum ProductionType {
	BULK = "BULK", // A granel
	UNIT = "UNIT", // Por unidad
}

export interface MaterialItem {
	id: string;
	productId: string;
	quantity: number;
}

export interface ProductionInfo {
	productionType: ProductionType;
	recipeId: string | null;
	materials: MaterialItem[];
}

const defaultProductionInfo: ProductionInfo = {
	productionType: ProductionType.BULK,
	recipeId: null,
	materials: [],
};

export const productionInfoStore = new Store<ProductionInfo>(defaultProductionInfo);

export const productionInfoStoreActions = {
	setProductionType: (type: ProductionType) => {
		productionInfoStore.setState((prev) => ({
			...prev,
			productionType: type,
			// Reset recipe and materials when changing production type
			recipeId: null,
			materials: type === ProductionType.UNIT ? [] : prev.materials,
		}));
	},
	
	setRecipeId: (recipeId: string | null) => {
		productionInfoStore.setState((prev) => ({
			...prev,
			recipeId,
		}));
	},
	
	addMaterial: () => {
		productionInfoStore.setState((prev) => ({
			...prev,
			materials: [
				...prev.materials,
				{
					id: `material-${Date.now()}-${Math.random()}`,
					productId: "",
					quantity: 0,
				},
			],
		}));
	},
	
	updateMaterial: (id: string, updates: Partial<Omit<MaterialItem, 'id'>>) => {
		productionInfoStore.setState((prev) => ({
			...prev,
			materials: prev.materials.map((material) =>
				material.id === id ? { ...material, ...updates } : material
			),
		}));
	},
	
	removeMaterial: (id: string) => {
		productionInfoStore.setState((prev) => ({
			...prev,
			materials: prev.materials.filter((material) => material.id !== id),
		}));
	},
	
	resetProductionInfo: () => {
		productionInfoStore.setState(() => defaultProductionInfo);
	},
};
