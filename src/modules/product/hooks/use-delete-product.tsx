import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Product } from "../service/model/product";
import { productOptions } from "./product-options";

export default function useDeleteProduct() {
	const service = useService();
	const { product } = service;
	const queryClient = useQueryClient();
	const queryKey = productOptions(service).queryKey;

	return useMutation({
		mutationFn: (id: string) => AppRuntime.runPromise(product.delete(id)),
		onMutate: async (deletedId) => {
			await queryClient.cancelQueries({ queryKey });
			const previousProducts = queryClient.getQueryData<Product[]>(queryKey);

			queryClient.setQueryData<Product[]>(queryKey, (old) =>
				create(old, (draft) => {
					if (draft) {
						const index = draft.findIndex((p) => p.id === deletedId);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}
				}),
			);

			return { previousProducts };
		},
		onError: (err, deletedId, context) => {
			if (context?.previousProducts) {
				queryClient.setQueryData(queryKey, context.previousProducts);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
