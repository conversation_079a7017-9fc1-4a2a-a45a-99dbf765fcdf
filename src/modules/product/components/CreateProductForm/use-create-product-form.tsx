import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import { brandOptions } from "src/modules/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "src/modules/category/hooks/category-options";
import { CategoryCode } from "~/modules/category/service/model/category";
import { measurementUnitOptions } from "~/modules/measurement-unit/hooks/measurement-unit-options";
import useCreateProduct from "../../hooks/use-create-product";
import { CreateProductSchema } from "./schema";

const defaultValues = {
	name: "",
	commercialName: "",
	code: "",
	skuCode: "",
	brandID: "",
	measurementUnitID: "",
	categoryIDs: [],
	state: "ACTIVE",
	description: "",
	canBeSold: true,
	canBePurchased: false,
} as CreateProductSchema;

export default function useCreateProductForm() {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useCreateProduct();

	// Fetch required data for dropdowns
	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);
	const { data: categories = [] } = useQuery(categoryOptions(service));

	// Find the products parent category
	const productParentCategory = categories.find(
		(cat) => cat.code === CategoryCode.PRODUCTS,
	);

	// Fetch subcategories for products
	const { data: productSubcategories = [] } = useQuery(
		categorySubcategoriesOptions(service, productParentCategory?.id || ""),
	);

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateProductSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: productParentCategory?.id
						? [...value.categoryIDs, productParentCategory?.id]
						: value.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
					costPriceTotal: value.costPriceTotal,
				},
				{
					onSuccess: () => {
						toast.success("Producto creado exitosamente");
						navigate({ to: "/admin/products/products" });
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	return {
		form,
		isPending,
		brands,
		measurementUnits,
		productSubcategories,
		productParentCategory,
	};
}
