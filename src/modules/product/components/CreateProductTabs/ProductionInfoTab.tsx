import { useStore } from "@tanstack/react-store";
import { Plus, Trash2 } from "lucide-react";
import { CategoryCode } from "~/modules/category/service/model/category";
import {
	ProductionType,
	productionInfoStore,
	productionInfoStoreActions,
} from "../../store/productionInfo";
import { useQuery } from "@tanstack/react-query";
import { productOptionsByCategoryCode } from "../../hooks/product-options";
import { useService } from "~/config/context/serviceProvider";

// Mock recipes data - replace with actual data later
const mockBulkRecipes = [
	{ id: "recipe-1", name: "Receta de Masa Base" },
	{ id: "recipe-2", name: "Receta de Salsa Especial" },
	{ id: "recipe-3", name: "Receta de Mezcla Premium" },
];

const mockUnitRecipes = [
	{ id: "recipe-unit-1", name: "Receta Individual A" },
	{ id: "recipe-unit-2", name: "Receta Individual B" },
	{ id: "recipe-unit-3", name: "Receta Individual C" },
];

export default function ProductionInfoTab() {
	const service = useService();
	const productionInfo = useStore(productionInfoStore);
	const { data: materials = [] } = useQuery(productOptionsByCategoryCode(service, CategoryCode.MATERIALS));

	const availableRecipes =
		productionInfo.productionType === ProductionType.BULK
			? mockBulkRecipes
			: mockUnitRecipes;

	return (
		<div className="space-y-6">
			{/* Production Type Switch */}
			<div>
				<span className="label font-medium">Tipo de Producción</span>
				<div className="flex gap-4">
					<label className="label cursor-pointer">
						<input
							type="radio"
							name="productionType"
							className="radio radio-primary"
							checked={productionInfo.productionType === ProductionType.BULK}
							onChange={() =>
								productionInfoStoreActions.setProductionType(
									ProductionType.BULK,
								)
							}
						/>
						<span className="label-text ml-2">A Granel</span>
					</label>
					<label className="label cursor-pointer">
						<input
							type="radio"
							name="productionType"
							className="radio radio-primary"
							checked={productionInfo.productionType === ProductionType.UNIT}
							onChange={() =>
								productionInfoStoreActions.setProductionType(
									ProductionType.UNIT,
								)
							}
						/>
						<span className="label-text ml-2">Por Unidad</span>
					</label>
				</div>
			</div>

			{/* Recipe Selection */}
			<div>
				<span className="label font-medium">Receta</span>
				<select
					className="select select-bordered w-full"
					value={productionInfo.recipeId || ""}
					onChange={(e) =>
						productionInfoStoreActions.setRecipeId(e.target.value || null)
					}
				>
					<option value="">Seleccionar receta</option>
					{availableRecipes.map((recipe) => (
						<option key={recipe.id} value={recipe.id}>
							{recipe.name}
						</option>
					))}
				</select>
			</div>

			{/* Materials Section - Only for Bulk Production */}
			{productionInfo.productionType === ProductionType.BULK && (
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h3 className="font-medium text-lg">Materiales</h3>
						<button
							type="button"
							className="btn btn-primary btn-sm"
							onClick={productionInfoStoreActions.addMaterial}
						>
							<Plus size={16} />
							Agregar Material
						</button>
					</div>

					{productionInfo.materials.length === 0 ? (
						<div className="py-8 text-center text-gray-500">
							No hay materiales agregados. Haz clic en "Agregar Material" para
							comenzar.
						</div>
					) : (
						<div className="space-y-3">
							{productionInfo.materials.map((material) => (
								<div key={material.id} className="flex items-end gap-4">
									<div className="flex-1">
										<span className="label">Material</span>
										<select
											className="select select-bordered w-full"
											value={material.productId}
											onChange={(e) =>
												productionInfoStoreActions.updateMaterial(material.id, {
													productId: e.target.value,
												})
											}
										>
											<option value="">Seleccionar material</option>
											{materials.map((mat) => (
												<option key={mat.id} value={mat.id}>
													{mat.name} ({mat.code})
												</option>
											))}
										</select>
									</div>
									<div className="w-32">
										<span className="label">Cantidad</span>
										<input
											type="number"
											className="input input-bordered w-full"
											placeholder="0"
											min="0"
											step="0.01"
											value={material.quantity || ""}
											onChange={(e) =>
												productionInfoStoreActions.updateMaterial(material.id, {
													quantity: Number.parseFloat(e.target.value) || 0,
												})
											}
										/>
									</div>
									<button
										type="button"
										className="btn btn-error btn-sm"
										onClick={() =>
											productionInfoStoreActions.removeMaterial(material.id)
										}
									>
										<Trash2 size={16} />
									</button>
								</div>
							))}
						</div>
					)}
				</div>
			)}
		</div>
	);
}
