import { useStore } from "@tanstack/react-store";
import { cn } from "~/core/utils/classes";
import {
	CreateProductTabType,
	createProductTabStore,
	createProductTabStoreActions,
} from "../../store/createProductTab";
import CreateProductForm from "../CreateProductForm";
import ProductionInfoTab from "./ProductionInfoTab";

export default function CreateProductTabs() {
	const selectedTab = useStore(createProductTabStore);

	return (
		<div className="space-y-6">
			{/* Tab Navigation */}
			<div className="card bg-base-300">
				<div className="card-body">
					<div className="tabs tabs-box">
						<button
							type="button"
							className={cn(
								"tab w-1/2",
								selectedTab === CreateProductTabType.PRODUCT_INFO &&
									"tab-active",
							)}
							onClick={() =>
								createProductTabStoreActions.setTab(
									CreateProductTabType.PRODUCT_INFO,
								)
							}
						>
							Información del Producto
						</button>
						<button
							type="button"
							className={cn(
								"tab w-1/2",
								selectedTab === CreateProductTabType.PRODUCTION_INFO &&
									"tab-active",
							)}
							onClick={() =>
								createProductTabStoreActions.setTab(
									CreateProductTabType.PRODUCTION_INFO,
								)
							}
						>
							Información de Producción
						</button>
					</div>
				</div>
			</div>

			{/* Tab Content */}
			{selectedTab === CreateProductTabType.PRODUCT_INFO && (
				<div className="card bg-base-300 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">Información del Producto</h2>
						<CreateProductForm />
					</div>
				</div>
			)}

			{selectedTab === CreateProductTabType.PRODUCTION_INFO && (
				<div className="card bg-base-300 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">Información de Producción</h2>
						<ProductionInfoTab />
					</div>
				</div>
			)}
		</div>
	);
}
