import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const brandOptions = ({ brand }: serviceRegistry) =>
	queryOptions({
		queryKey: ["brands"],
		queryFn: () => AppRuntime.runPromise(brand.getAll()),
	});

export const brandOptionsById = ({ brand }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["brands", id],
		queryFn: () => AppRuntime.runPromise(brand.getById(id)),
	});
