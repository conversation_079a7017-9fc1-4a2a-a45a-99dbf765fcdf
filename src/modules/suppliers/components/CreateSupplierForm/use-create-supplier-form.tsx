import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { brandOptions } from "~/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "~/category/hooks/category-options";
import { CategoryCode } from "~/category/service/model/category";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import { measurementUnitOptions } from "~/measurement-unit/hooks/measurement-unit-options";
import useCreateProduct from "~/product/hooks/use-create-product";
import { CreateSupplierSchema } from "./schema";

const defaultValues = {
	canBeSold: false,
	canBePurchased: true,
} as CreateSupplierSchema;

export default function useCreateSupplierForm() {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useCreateProduct();

	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);
	const { data: categories = [] } = useQuery(categoryOptions(service));

	const supplierCategory = categories.find(
		(cat) => cat.code === CategoryCode.SUPPLIERS,
	);

	const { data: supplierSubcategories = [] } = useQuery({
		...categorySubcategoriesOptions(service, supplierCategory?.id || ""),
		enabled: !!supplierCategory?.id,
	});

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateSupplierSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: supplierCategory?.id
						? [...value.categoryIDs, supplierCategory?.id]
						: value.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
					costPriceTotal: value.costPriceTotal,
				},
				{
					onSuccess: () => {
						toast.success("Insumo creado exitosamente");
						navigate({ to: "/admin/products/suppliers" });
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	return {
		form,
		isPending,
		brands,
		measurementUnits,
		supplierSubcategories,
	};
}
