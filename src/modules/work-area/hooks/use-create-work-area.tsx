import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { CreateWorkArea, WorkArea } from "../service/model/work-area";
import { workAreaOptions } from "./work-area-options";

export default function useCreateWorkArea() {
	const service = useService();
	const { workArea } = service;
	const queryClient = useQueryClient();
	const queryKey = workAreaOptions(service).queryKey;

	return useMutation({
		mutationKey: ["create-work-area"],
		mutationFn: (newWorkArea: CreateWorkArea) =>
			AppRuntime.runPromise(workArea.create(newWorkArea)),
		onMutate: async (newWorkArea) => {
			await queryClient.cancelQueries({ queryKey });

			const previousWorkAreas = queryClient.getQueryData(queryKey);

			if (previousWorkAreas) {
				queryClient.setQueryData(
					queryKey,
					create(previousWorkAreas, (draft) => {
						draft.push({
							id: "new",
							name: newWorkArea.name,
							code: newWorkArea.code,
							createdAt: null,
							updatedAt: null,
							deletedAt: null,
						} as WorkArea);
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, [
					{
						id: "new",
						name: newWorkArea.name,
						code: newWorkArea.code,
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					} as WorkArea,
				]);
			}

			return { previousWorkAreas };
		},
		onError: (_error, _newWorkArea, context) => {
			if (context?.previousWorkAreas) {
				queryClient.setQueryData(queryKey, context.previousWorkAreas);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
