import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { UpdateWorkArea, WorkArea } from "../service/model/work-area";
import { workAreaOptions } from "./work-area-options";

export default function useUpdateWorkArea() {
	const service = useService();
	const { workArea } = service;
	const queryClient = useQueryClient();
	const queryKey = workAreaOptions(service).queryKey;

	return useMutation({
		mutationKey: ["update-work-area"],
		mutationFn: (updatedWorkArea: UpdateWorkArea) =>
			AppRuntime.runPromise(workArea.update(updatedWorkArea)),
		onMutate: async (updatedWorkArea) => {
			await queryClient.cancelQueries({ queryKey });

			const previousWorkAreas = queryClient.getQueryData(queryKey);

			if (previousWorkAreas) {
				queryClient.setQueryData(
					queryKey,
					create(previousWorkAreas, (draft) => {
						const index = draft.findIndex(
							(item) => item.id === updatedWorkArea.id,
						);
						if (index !== -1) {
							draft[index] = {
								...draft[index],
								name: updatedWorkArea.name,
								code: updatedWorkArea.code,
							} as WorkArea;
						}
					}),
				);
			}

			return { previousWorkAreas };
		},
		onError: (_error, _updatedWorkArea, context) => {
			if (context?.previousWorkAreas) {
				queryClient.setQueryData(queryKey, context.previousWorkAreas);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
