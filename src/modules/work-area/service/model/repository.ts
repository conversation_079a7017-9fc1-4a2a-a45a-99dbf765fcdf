import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type { CreateWorkArea, UpdateWorkArea, WorkArea } from "./work-area";

export class WorkAreaRepository extends Effect.Tag("WorkAreaRepository")<
	WorkAreaRepository,
	{
		readonly getAll: () => Effect.Effect<WorkArea[], AppError>;
		readonly getById: (id: string) => Effect.Effect<WorkArea, AppError>;
		readonly create: (
			workArea: CreateWorkArea,
		) => Effect.Effect<string, AppError>;
		readonly update: (
			workArea: UpdateWorkArea,
		) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
	}
>() {}
