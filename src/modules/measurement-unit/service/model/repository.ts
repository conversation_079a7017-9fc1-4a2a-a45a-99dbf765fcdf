import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type {
	CreateMeasurementUnit,
	MeasurementUnit,
	UpdateMeasurementUnit,
} from "./measurement-unit";

export class MeasurementUnitRepository extends Effect.Tag(
	"MeasurementUnitRepository",
)<
	MeasurementUnitRepository,
	{
		readonly getAll: () => Effect.Effect<MeasurementUnit[], AppError>;
		readonly getById: (id: string) => Effect.Effect<MeasurementUnit, AppError>;
		readonly create: (
			measurementUnit: CreateMeasurementUnit,
		) => Effect.Effect<string, AppError>;
		readonly update: (
			measurementUnit: UpdateMeasurementUnit,
		) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
	}
>() {}
