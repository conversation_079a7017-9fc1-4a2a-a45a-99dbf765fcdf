import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type {
	CreateMeasurementUnit,
	MeasurementUnit,
} from "../service/model/measurement-unit";
import { measurementUnitOptions } from "./measurement-unit-options";

export default function useCreateMeasurementUnit() {
	const service = useService();
	const { measurementUnit } = service;
	const queryClient = useQueryClient();
	const queryKey = measurementUnitOptions(service).queryKey;

	return useMutation({
		mutationKey: ["create-measurement-unit"],
		mutationFn: (newMeasurementUnit: CreateMeasurementUnit) =>
			AppRuntime.runPromise(measurementUnit.create(newMeasurementUnit)),
		onMutate: async (newMeasurementUnit) => {
			await queryClient.cancelQueries({ queryKey });

			const previousMeasurementUnits = queryClient.getQueryData(queryKey);

			if (previousMeasurementUnits) {
				queryClient.setQueryData(
					queryKey,
					create(previousMeasurementUnits, (draft) => {
						draft.push({
							id: "new",
							name: newMeasurementUnit.name,
							code: newMeasurementUnit.code,
							createdAt: null,
							updatedAt: null,
							deletedAt: null,
						} as MeasurementUnit);
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, [
					{
						id: "new",
						name: newMeasurementUnit.name,
						code: newMeasurementUnit.code,
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					} as MeasurementUnit,
				]);
			}

			return { previousMeasurementUnits };
		},
		onError: (_err, _newMeasurementUnit, context) => {
			queryClient.setQueryData(queryKey, context?.previousMeasurementUnits);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
