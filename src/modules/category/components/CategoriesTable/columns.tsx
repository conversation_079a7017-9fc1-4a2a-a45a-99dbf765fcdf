import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import { useState } from "react";
import type { Category } from "../../service/model/category";
import DeleteCategoryModal from "../DeleteCategoryModal";
import EditCategoryModal from "../EditCategoryModal";

const columnHelper = createColumnHelper<Category>();

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("code", {
		header: "Código",
		cell: (info) => info.getValue(),
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isEditOpen, setIsEditOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const category = row.original;

			return (
				<div className="flex gap-2">
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<EditCategoryModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={category.id}
					/>
					<DeleteCategoryModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						category={category}
					/>
				</div>
			);
		},
	}),
];
