import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "src/core/components/tables/BasicTable";
import type { Category } from "../../service/model/category";
import { columns } from "./columns";

interface TableProps {
	categories: Category[];
}

export default function Table({ categories }: TableProps) {
	const table = useReactTable({
		data: categories,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});

	return <BasicTable table={table} />;
}
