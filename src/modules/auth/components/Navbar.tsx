import { useNavigate } from "@tanstack/react-router";
import { User } from "lucide-react";
import ThemeSwitcher from "src/core/components/ThemeSwitcher";
import { useLogout } from "../hooks/use-logout";

export default function Navbar() {
	const { mutate } = useLogout();
	const navigate = useNavigate();

	const handleLogout = () => {
		mutate(undefined, {
			onSettled: () => {
				navigate({
					to: "/login",
				});
			},
		});
	};

	return (
		<div className="navbar w-full bg-neutral">
			<div className="flex-1">
				<button
					type="button"
					className="btn btn-ghost text-neutral-content text-xl"
				>
					Fhyona
				</button>
			</div>
			<div className="flex gap-4">
				<ThemeSwitcher className="w-30" />
				<div className="flex-none text-neutral-content">
					<div className="dropdown dropdown-end">
						<button type="button" className="btn btn-circle avatar">
							<User size={26} />
						</button>
						<ul className="menu menu-sm dropdown-content z-1 mt-3 w-52 rounded-box bg-neutral p-2 shadow">
							<li>
								<button type="button" onClick={handleLogout}>
									Logout
								</button>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	);
}
