import { Schema } from "effect";
import {
	Activity,
	ActivityCreateRequest,
	ActivityDetail,
	CreateProductionFlow,
	OperationInfo,
	ProductionFlow,
	ProductionFlowCreateWithActivities,
	ProductionFlowWithActivities,
	UpdateProductionFlow,
	WorkAreaInfo,
} from "../../model/production-flow";

// API schemas for ProductionFlow
export const ProductionFlowApi = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	name: Schema.String,
	recipe_id: Schema.NullOr(Schema.String),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ProductionFlowFromApi = Schema.transform(
	ProductionFlowApi,
	ProductionFlow,
	{
		strict: true,
		decode: (productionFlowApi) => ({
			...productionFlowApi,
			recipeID: productionFlowApi.recipe_id,
			createdAt: productionFlowApi.created_at,
			updatedAt: productionFlowApi.updated_at,
			deletedAt: productionFlowApi.deleted_at,
		}),
		encode: (productionFlow) => ({
			...productionFlow,
			recipe_id: productionFlow.recipeID,
			created_at: productionFlow.createdAt,
			updated_at: productionFlow.updatedAt,
			deleted_at: productionFlow.deletedAt,
		}),
	},
);

export const ProductionFlowListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(ProductionFlowFromApi))),
	Schema.mutable(Schema.Array(ProductionFlow)),
	{
		strict: true,
		decode: (productionFlowApiList) =>
			productionFlowApiList ? productionFlowApiList : [],
		encode: (productionFlowList) => productionFlowList,
	},
);

// API schemas for Activity
export const ActivityApi = Schema.Struct({
	id: Schema.String,
	production_flow_id: Schema.String,
	work_area_id: Schema.String,
	operation_id: Schema.String,
	index_number: Schema.Number,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ActivityFromApi = Schema.transform(ActivityApi, Activity, {
	strict: true,
	decode: (activityApi) => ({
		id: activityApi.id,
		productionFlowId: activityApi.production_flow_id,
		workAreaId: activityApi.work_area_id,
		operationId: activityApi.operation_id,
		indexNumber: activityApi.index_number,
		createdAt: activityApi.created_at,
		updatedAt: activityApi.updated_at,
		deletedAt: activityApi.deleted_at,
	}),
	encode: (activity) => ({
		id: activity.id,
		production_flow_id: activity.productionFlowId,
		work_area_id: activity.workAreaId,
		operation_id: activity.operationId,
		index_number: activity.indexNumber,
		created_at: activity.createdAt,
		updated_at: activity.updatedAt,
		deleted_at: activity.deletedAt,
	}),
});

// API schemas for WorkAreaInfo and OperationInfo
export const WorkAreaInfoApi = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	name: Schema.String,
});

export const WorkAreaInfoFromApi = Schema.transform(
	WorkAreaInfoApi,
	WorkAreaInfo,
	{
		strict: true,
		decode: (workAreaInfoApi) => workAreaInfoApi,
		encode: (workAreaInfo) => workAreaInfo,
	},
);

export const OperationInfoApi = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	name: Schema.String,
});

export const OperationInfoFromApi = Schema.transform(
	OperationInfoApi,
	OperationInfo,
	{
		strict: true,
		decode: (operationInfoApi) => operationInfoApi,
		encode: (operationInfo) => operationInfo,
	},
);

// API schema for ActivityDetail - Updated to match new Go API response structure
export const ActivityDetailApi = Schema.Struct({
	id: Schema.String,
	index_number: Schema.Number,
	work_area: WorkAreaInfoFromApi,
	operation: OperationInfoFromApi,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ActivityDetailFromApi = Schema.transform(
	ActivityDetailApi,
	ActivityDetail,
	{
		strict: true,
		decode: (activityDetailApi) => ({
			id: activityDetailApi.id,
			indexNumber: activityDetailApi.index_number,
			workArea: activityDetailApi.work_area,
			operation: activityDetailApi.operation,
			createdAt: activityDetailApi.created_at,
			updatedAt: activityDetailApi.updated_at,
			deletedAt: activityDetailApi.deleted_at,
		}),
		encode: (activityDetail) => ({
			id: activityDetail.id,
			index_number: activityDetail.indexNumber,
			work_area: activityDetail.workArea,
			operation: activityDetail.operation,
			created_at: activityDetail.createdAt,
			updated_at: activityDetail.updatedAt,
			deleted_at: activityDetail.deletedAt,
		}),
	},
);

// API schema for ProductionFlowWithActivities
export const ProductionFlowWithActivitiesApi = Schema.Struct({
	production_flow: ProductionFlowFromApi,
	activities: Schema.Array(ActivityDetailFromApi),
});

export const ProductionFlowWithActivitiesFromApi = Schema.transform(
	ProductionFlowWithActivitiesApi,
	ProductionFlowWithActivities,
	{
		strict: true,
		decode: (productionFlowWithActivitiesApi) => ({
			productionFlow: productionFlowWithActivitiesApi.production_flow,
			activities: productionFlowWithActivitiesApi.activities,
		}),
		encode: (productionFlowWithActivities) => ({
			production_flow: productionFlowWithActivities.productionFlow,
			activities: productionFlowWithActivities.activities,
		}),
	},
);

// Create schemas
export const CreateProductionFlowApi = Schema.Struct({
	code: Schema.String,
	name: Schema.String,
	recipe_id: Schema.NullOr(Schema.String),
});

export const CreateProductionFlowApiFromCreateProductionFlow = Schema.transform(
	CreateProductionFlow,
	CreateProductionFlowApi,
	{
		strict: true,
		decode: (createProductionFlow) => ({
			...createProductionFlow,
			recipe_id: createProductionFlow.recipeID,
		}),
		encode: (createProductionFlowApi) => ({
			...createProductionFlowApi,
			recipeID: createProductionFlowApi.recipe_id,
		}),
	},
);

export const UpdateProductionFlowApi = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	name: Schema.String,
	recipe_id: Schema.NullOr(Schema.String),
});

export const UpdateProductionFlowApiFromUpdateProductionFlow = Schema.transform(
	UpdateProductionFlow,
	UpdateProductionFlowApi,
	{
		strict: true,
		decode: (updateProductionFlow) => ({
			...updateProductionFlow,
			recipe_id: updateProductionFlow.recipeID,
		}),
		encode: (updateProductionFlowApi) => ({
			...updateProductionFlowApi,
			recipeID: updateProductionFlowApi.recipe_id,
		}),
	},
);

// ActivityCreateRequest API schema
export const ActivityCreateRequestApi = Schema.Struct({
	work_area_id: Schema.String,
	operation_id: Schema.String,
	index_number: Schema.Number,
});

export const ActivityCreateRequestApiFromActivityCreateRequest =
	Schema.transform(ActivityCreateRequest, ActivityCreateRequestApi, {
		strict: true,
		decode: (activityCreateRequest) => ({
			work_area_id: activityCreateRequest.workAreaId,
			operation_id: activityCreateRequest.operationId,
			index_number: activityCreateRequest.indexNumber,
		}),
		encode: (activityCreateRequestApi) => ({
			workAreaId: activityCreateRequestApi.work_area_id,
			operationId: activityCreateRequestApi.operation_id,
			indexNumber: activityCreateRequestApi.index_number,
		}),
	});

// ProductionFlowCreateWithActivities API schema
export const ProductionFlowCreateWithActivitiesApi = Schema.Struct({
	code: Schema.String,
	name: Schema.String,
	recipe_id: Schema.NullOr(Schema.String),
	activities: Schema.Array(ActivityCreateRequestApiFromActivityCreateRequest),
});

export const ProductionFlowCreateWithActivitiesApiFromProductionFlowCreateWithActivities =
	Schema.transform(
		ProductionFlowCreateWithActivities,
		ProductionFlowCreateWithActivitiesApi,
		{
			strict: true,
			decode: (productionFlowCreateWithActivities) => ({
				code: productionFlowCreateWithActivities.code,
				name: productionFlowCreateWithActivities.name,
				recipe_id: productionFlowCreateWithActivities.recipeID,
				activities: productionFlowCreateWithActivities.activities,
			}),
			encode: (productionFlowCreateWithActivitiesApi) => ({
				code: productionFlowCreateWithActivitiesApi.code,
				name: productionFlowCreateWithActivitiesApi.name,
				recipeID: productionFlowCreateWithActivitiesApi.recipe_id,
				activities: productionFlowCreateWithActivitiesApi.activities,
			}),
		},
	);

export const CreateProductionFlowApiResponse = Schema.String;
