import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useCreateProductionFlowWithActivities from "../../hooks/use-create-production-flow-with-activities";
import type { ActivitySchema } from "../schemas";
import { ProductionFlowSchema } from "../schemas";

interface UseCreateProductionFlowPageProps {
	activities: ActivitySchema[];
}

const defaultValues = {
	name: "",
	code: "",
	recipeID: "",
} as ProductionFlowSchema;

export default function useCreateProductionFlowPage({
	activities,
}: UseCreateProductionFlowPageProps) {
	const navigate = useNavigate();
	const { mutate, isPending } = useCreateProductionFlowWithActivities();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: ProductionFlowSchema,
		},
		onSubmit: ({ value }) => {
			// This will be handled by the handleSubmit function
		},
	});

	const handleSubmit = () => {
		// Validate form first
		form.handleSubmit();

		// Check if form is valid
		if (form.state.errors.length > 0) {
			toast.error("Por favor corrige los errores en el formulario");
			return;
		}

		// Check if activities are present
		if (activities.length === 0) {
			toast.error("Debe agregar al menos una actividad");
			return;
		}

		// Get form values
		const formValues = form.state.values;

		// Transform activities to the format expected by the API
		const activitiesForApi = activities.map((activity) => ({
			workAreaId: activity.workAreaId,
			operationId: activity.operationId,
			indexNumber: activity.indexNumber,
		}));

		// Create the production flow with activities
		mutate(
			{
				name: formValues.name,
				code: formValues.code,
				recipeID: formValues.recipeID,
				activities: activitiesForApi,
			},
			{
				onSuccess: () => {
					toast.success("Flujo de producción creado exitosamente");
					navigate({ to: "/admin/manufacture/production-flow" });
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			},
		);
	};

	return {
		form,
		handleSubmit,
		isPending,
	};
}
