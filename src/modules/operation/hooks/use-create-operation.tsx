import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { CreateOperation, Operation } from "../service/model/operation";
import { operationOptions } from "./operation-options";

export default function useCreateOperation() {
	const service = useService();
	const { operation } = service;
	const queryClient = useQueryClient();
	const queryKey = operationOptions(service).queryKey;

	return useMutation({
		mutationKey: ["create-operation"],
		mutationFn: (createOperation: CreateOperation) =>
			AppRuntime.runPromise(operation.create(createOperation)),
		onMutate: async (createOperation) => {
			await queryClient.cancelQueries({ queryKey });

			const previousOperations = queryClient.getQueryData(queryKey);

			if (previousOperations) {
				const tempId = `temp-${Date.now()}`;
				const newOperation: Operation = {
					id: tempId,
					name: createOperation.name,
					code: createOperation.code,
					createdAt: new Date().toISOString(),
					updatedAt: null,
					deletedAt: null,
				};

				queryClient.setQueryData(
					queryKey,
					create(previousOperations, (draft) => {
						draft.push(newOperation);
					}),
				);
			}

			return { previousOperations };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousOperations);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
