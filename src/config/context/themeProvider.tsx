import { useRouter } from "@tanstack/react-router";
import { type PropsWithChildren, createContext, use } from "react";
import { setThemeServerFn } from "~/auth/server/theme";

type ThemeContextVal = { theme: string; setTheme: (val: string) => void };
type Props = PropsWithChildren<{ theme: string }>;

const ThemeContext = createContext<ThemeContextVal | null>(null);

export function ThemeProvider({ children, theme }: Props) {
	const router = useRouter();

	function setTheme(val: string) {
		setThemeServerFn({ data: val });
		router.invalidate();
	}

	return <ThemeContext value={{ theme, setTheme }}>{children}</ThemeContext>;
}

export function useTheme() {
	const val = use(ThemeContext);
	if (!val) throw new Error("useTheme called outside of ThemeProvider!");
	return val;
}
