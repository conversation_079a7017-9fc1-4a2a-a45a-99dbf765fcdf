import type { LoginCredentials } from "~/auth/service/model/auth";
import { AuthUsecase } from "~/auth/service/model/usecase";
import type { CreateBrand, UpdateBrand } from "~/brand/service/model/brand";
import { BrandUsecase } from "~/brand/service/model/usecase";
import type {
	CategoryCreate,
	CategoryUpdate,
} from "~/category/service/model/category";
import { CategoryUsecase } from "~/category/service/model/usecase";
import type {
	CreateMeasurementUnit,
	UpdateMeasurementUnit,
} from "~/measurement-unit/service/model/measurement-unit";
import { MeasurementUnitUsecase } from "~/measurement-unit/service/model/usecase";
import type {
	CreateOperation,
	UpdateOperation,
} from "~/operation/service/model/operation";
import { OperationUsecase } from "~/operation/service/model/usecase";
import type {
	CreateProduct,
	UpdateProduct,
} from "~/product/service/model/product";
import { ProductUsecase } from "~/product/service/model/usecase";
import type {
	CreateProductionFlow,
	ProductionFlowCreateWithActivities,
	UpdateProductionFlow,
} from "~/production-flow/service/model/production-flow";
import { ProductionFlowUsecase } from "~/production-flow/service/model/usecase";
import type {
	CreateRecipe,
	UpdateRecipe,
} from "~/recipe/service/model/recipe";
import { RecipeUsecase } from "~/recipe/service/model/usecase";
import { WorkAreaUsecase } from "~/work-area/service/model/usecase";
import type {
	CreateWorkArea,
	UpdateWorkArea,
} from "~/work-area/service/model/work-area";

const authService = {
	login: (credentials: LoginCredentials) => AuthUsecase.login(credentials),
	logout: () => AuthUsecase.logout(),
	getSession: () => AuthUsecase.getSession(),
};

const brandService = {
	create: (brand: CreateBrand) => BrandUsecase.create(brand),
	getAll: () => BrandUsecase.getAll(),
	getById: (id: string) => BrandUsecase.getById(id),
	update: (brand: UpdateBrand) => BrandUsecase.update(brand),
	delete: (id: string) => BrandUsecase.delete(id),
	validateCode: (code: string) => BrandUsecase.validateCode(code),
};

const categoryService = {
	create: (category: CategoryCreate) => CategoryUsecase.create(category),
	getAll: () => CategoryUsecase.getAll(),
	getById: (id: string) => CategoryUsecase.getById(id),
	update: (category: CategoryUpdate) => CategoryUsecase.update(category),
	delete: (id: string) => CategoryUsecase.delete(id),
	getSubcategories: (id: string) => CategoryUsecase.getSubcategories(id),
	getDetails: (id: string) => CategoryUsecase.getDetails(id),
	getParents: () => CategoryUsecase.getParents(),
	validateCode: (code: string) => CategoryUsecase.validateCode(code),
};

const measurementUnitService = {
	create: (measurementUnit: CreateMeasurementUnit) =>
		MeasurementUnitUsecase.create(measurementUnit),
	getAll: () => MeasurementUnitUsecase.getAll(),
	getById: (id: string) => MeasurementUnitUsecase.getById(id),
	update: (measurementUnit: UpdateMeasurementUnit) =>
		MeasurementUnitUsecase.update(measurementUnit),
	delete: (id: string) => MeasurementUnitUsecase.delete(id),
	validateCode: (code: string) => MeasurementUnitUsecase.validateCode(code),
};

const operationService = {
	create: (operation: CreateOperation) => OperationUsecase.create(operation),
	getAll: () => OperationUsecase.getAll(),
	getById: (id: string) => OperationUsecase.getById(id),
	update: (operation: UpdateOperation) => OperationUsecase.update(operation),
	delete: (id: string) => OperationUsecase.delete(id),
	validateCode: (code: string) => OperationUsecase.validateCode(code),
};

const workAreaService = {
	create: (workArea: CreateWorkArea) => WorkAreaUsecase.create(workArea),
	getAll: () => WorkAreaUsecase.getAll(),
	getById: (id: string) => WorkAreaUsecase.getById(id),
	update: (workArea: UpdateWorkArea) => WorkAreaUsecase.update(workArea),
	delete: (id: string) => WorkAreaUsecase.delete(id),
	validateCode: (code: string) => WorkAreaUsecase.validateCode(code),
};

const productService = {
	create: (product: CreateProduct) => ProductUsecase.create(product),
	getAll: () => ProductUsecase.getAll(),
	getById: (id: string) => ProductUsecase.getById(id),
	update: (product: UpdateProduct) => ProductUsecase.update(product),
	delete: (id: string) => ProductUsecase.delete(id),
	validateCode: (code: string) => ProductUsecase.validateCode(code),
	validateCommercialName: (commercialName: string) =>
		ProductUsecase.validateCommercialName(commercialName),
	validateSKUCode: (skuCode: string) => ProductUsecase.validateSKUCode(skuCode),
	getProductsByCategoryCode: (categoryCode: string) =>
		ProductUsecase.getProductsByCategoryCode(categoryCode),
};

const productionFlowService = {
	create: (productionFlow: CreateProductionFlow) =>
		ProductionFlowUsecase.create(productionFlow),
	createWithActivities: (productionFlow: ProductionFlowCreateWithActivities) =>
		ProductionFlowUsecase.createWithActivities(productionFlow),
	getAll: () => ProductionFlowUsecase.getAll(),
	getById: (id: string) => ProductionFlowUsecase.getById(id),
	update: (productionFlow: UpdateProductionFlow) =>
		ProductionFlowUsecase.update(productionFlow),
	delete: (id: string) => ProductionFlowUsecase.delete(id),
	getWithActivities: (id: string) =>
		ProductionFlowUsecase.getWithActivities(id),
	validateCode: (code: string) => ProductionFlowUsecase.validateCode(code),
};

const recipeService = {
	create: (recipe: CreateRecipe) => RecipeUsecase.create(recipe),
	getAll: () => RecipeUsecase.getAll(),
	getById: (id: string) => RecipeUsecase.getById(id),
	update: (recipe: UpdateRecipe) => RecipeUsecase.update(recipe),
	delete: (id: string) => RecipeUsecase.delete(id),
	validateCode: (code: string) => RecipeUsecase.validateCode(code),
	validateName: (name: string) => RecipeUsecase.validateName(name),
};

export const serviceRegistry = {
	auth: authService,
	brand: brandService,
	category: categoryService,
	measurementUnit: measurementUnitService,
	operation: operationService,
	product: productService,
	recipe: recipeService,
	workArea: workAreaService,
	productionFlow: productionFlowService,
};

export type serviceRegistry = typeof serviceRegistry;
