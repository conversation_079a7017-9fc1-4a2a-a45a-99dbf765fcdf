import type { HTMLInputTypeAttribute } from "react";
import { useFieldContext } from "./form";

export function FSTextField({
	label,
	placeholder,
	type = "text",
	prefixComponent,
	suffixComponent,
}: {
	label?: string;
	placeholder: string;
	type?: HTMLInputTypeAttribute;
	prefixComponent?: React.ReactNode;
	suffixComponent?: React.ReactNode;
}) {
	const field = useFieldContext<string | number>();

	const isTouched = field.state.meta.isTouched;
	const errorsLength = field.state.meta.errors.length;
	const isError = isTouched && errorsLength;
	const isValid =
		isTouched && !field.state.meta.isValidating && !!field.state.value;
	const errors = field.state.meta.errors;

	return (
		<fieldset className="fieldset">
			{label && <legend className="fieldset-legend">{label}</legend>}
			<div
				className={`input w-full ${isError ? "input-error" : isValid ? "input-success" : ""}`}
			>
				{prefixComponent && prefixComponent}
				<input
					type={type}
					className="grow"
					placeholder={placeholder}
					value={
						type === "number"
							? field.state.value?.toString()
							: field.state.value
					}
					onChange={(e) =>
						field.handleChange(
							// @ts-ignore
							type === "number" ? Number(e.target.value) : e.target.value,
						)
					}
				/>
				{suffixComponent && suffixComponent}
			</div>
			{isError
				? errors.flatMap(({ message }) => (
						<p key={message} className="fieldset-label text-error">
							{message}
						</p>
					))
				: null}
		</fieldset>
	);
}
