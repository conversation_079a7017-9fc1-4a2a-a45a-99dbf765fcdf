import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import CategoriesTable from "src/modules/category/components/CategoriesTable";
import CategoryTab from "src/modules/category/components/CategoryTab";
import CreateCategoryModal from "src/modules/category/components/CreateCategoryModal";

export const Route = createFileRoute("/_authed/admin/products/categories/")({
	component: RouteComponent,
});

function RouteComponent() {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<>
			<div className="container mx-auto flex max-w-4xl flex-col gap-4">
				<div className="card bg-base-300">
					<div className="card-body">
						<CategoryTab />
					</div>
				</div>
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<button
								type="button"
								className="btn btn-primary"
								onClick={() => setIsOpen(true)}
							>
								Crear Sub Categoria
							</button>
						</div>
						<CategoriesTable />
					</div>
				</div>
			</div>
			<CreateCategoryModal isOpen={isOpen} setIsOpen={setIsOpen} />
		</>
	);
}
