import { Link, createFileRoute } from "@tanstack/react-router";
import { Plus } from "lucide-react";
import ProductionDevicesTable from "~/modules/production-devices/components/ProductionDevicesTable";

export const Route = createFileRoute(
	"/_authed/admin/products/production-devices/",
)({
	component: RouteComponent,
});

function RouteComponent() {
	return (
		<>
			<div className="container mx-auto max-w-4xl">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<Link
								to="/admin/products/production-devices/create"
								className="btn btn-primary"
							>
								<Plus size={16} />
								Crear nuevo dispositivo
							</Link>
						</div>
					</div>
					<ProductionDevicesTable />
				</div>
			</div>
		</>
	);
}
