import { Link, createFileRoute } from "@tanstack/react-router";
import { Plus } from "lucide-react";
import ProductsTable from "~/modules/product/components/ProductsTable";

export const Route = createFileRoute("/_authed/admin/products/products/")({
	component: RouteComponent,
});

function RouteComponent() {
	return (
		<>
			<div className="container mx-auto max-w-4xl">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<Link
								to="/admin/products/products/create"
								className="btn btn-primary"
							>
								<Plus size={16} />
								Crear nuevo producto
							</Link>
						</div>
					</div>
					<ProductsTable />
				</div>
			</div>
		</>
	);
}
